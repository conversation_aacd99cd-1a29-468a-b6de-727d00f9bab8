import {
  IconDelete,
  IconDownload,
  IconEyeOpened,
  IconUpload,
} from "@douyinfe/semi-icons";
import {
  Button,
  Form,
  Image,
  Toast,
  useFormApi,
  useFormState,
} from "@douyinfe/semi-ui";
import { upload_url } from "config";
// import { ReadChunkFunc, mediaInfoFactory, type MediaInfo } from 'mediainfo.js';
import type { MediaInfo, ReadChunkFunc } from "mediainfo.js";
import mediaInfoFactory from "mediainfo.js";
import { type } from "ramda";
import { FC, useEffect, useRef, useState } from "react";
import { mediainfoTrackType } from "utils";

type UploadProps = {
  formField: string;
  field: string;
  label: string;
  multiple?: boolean;
  accept?: string;
  listType?: "list" | "picture";
  singleAsMultiple?: boolean;
  type?: "img" | "file";
  arrayProcessType?: "array" | "string";
  enableMediaInfo?: boolean;
  onSuccess?: (res: any) => void;
  disabled?: boolean;
  isRequired?: boolean;
  children?: React.ReactNode;
  maxSize?: number;
  helpTextStyle?: React.CSSProperties;
  showHelpText?: boolean;
};

export const Upload: FC<UploadProps> = (props) => {
  const formApi = useFormApi();
  const formState = useFormState();

  const mediaInfoRef = useRef<MediaInfo<"text">>();
  const [result, setResult] = useState("");

  function makeReadChunk(file: File): ReadChunkFunc {
    return async (chunkSize: number, offset: number) =>
      new Uint8Array(
        await file.slice(offset, offset + chunkSize).arrayBuffer()
      );
  }

  useEffect(() => {
    mediaInfoFactory() //object (default), JSON, XML, HTML, text
      .then((mi) => {
        mediaInfoRef.current = mi;
      })
      .catch((error: unknown) => {
        console.error(error);
      });

    return () => {
      if (mediaInfoRef.current) {
        mediaInfoRef.current.close();
      }
    };
  }, []);

  /* const readChunk = (file) => (chunkSize, offset) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (event) => {
        if (event.target.error) {
          reject(event.target.error)
        }
        resolve(new Uint8Array(event.target.result))
      }
      reader.readAsArrayBuffer(file.slice(offset, offset + chunkSize))
    }) */

  const mediaInfoCallback = (result) => {
    console.log("file parsed", result);
    const generalTrack = result?.media?.track?.filter(
      (track) => track["@type"] === mediainfoTrackType.general
    );
    const videoTrack = result?.media?.track?.filter(
      (track) => track["@type"] === mediainfoTrackType.video
    );
    const audioTrack = result?.media?.track?.filter(
      (track) => track["@type"] === mediainfoTrackType.audio
    );
    const textTrack = result?.media?.track?.filter(
      (track) => track["@type"] === mediainfoTrackType.text
    );
    const imageTrack = result?.media?.track?.filter(
      (track) => track["@type"] === mediainfoTrackType.image
    );
    const otherTrack = result?.media?.track?.filter(
      (track) => track["@type"] === mediainfoTrackType.other
    );
    const res = {};
    if (generalTrack && generalTrack.length > 0) {
      console.log(generalTrack[0]);
      res.duration = generalTrack[0].Duration; // 时长
    }
    return res;
  };

  const getMediaInfo = async (file) => {
    // console.log(event?.target.files);
    // console.log(event?.target.files[0]);
    // const file = event?.target.files[0];
    console.log("file", file);
    if (file && mediaInfoRef.current) {
      const processingMediaInfoAnalysis =
        await mediaInfoRef.current.analyzeData(file.size, makeReadChunk(file));
      console.log(processingMediaInfoAnalysis);

      return mediaInfoCallback(processingMediaInfoAnalysis);

      /* .then(setResult)
      .catch((error: unknown) => {
        console.error(error)
      }) */
    } else {
      console.error(
        "No file or MediaInfo instance",
        file,
        mediaInfoRef.current
      );
    }

    /* MediaInfo().then((mediainfo) => {
      mediainfo
        .analyzeData(() => file.size, readChunk(file))
        .then((result) => {
          console.log(result);
        })
    }) */
  };

  const updateFormUris = (uris) => {
    if (!props.arrayProcessType) {
      //默认还是string
      formApi.setValue(props.formField, JSON.stringify(uris));
    } else if (props.arrayProcessType === "array") {
      // 前后端统一接口：JSON标准格式，后端内部转换为字符串
      formApi.setValue(props.formField, uris);
    } else if (props.arrayProcessType === "string") {
      //设置了string
      formApi.setValue(props.formField, JSON.stringify(uris));
    } else {
      //未设置，默认还是string
      formApi.setValue(props.formField, JSON.stringify(uris));
    }
  };

  const onSuccess = async (responseBody, file, fileList) => {
    console.log("onSuccess", responseBody, file, fileList);

    let mediaInfo = {};
    if (props.enableMediaInfo && file) {
      mediaInfo = await getMediaInfo(file);
      console.log(mediaInfo);
    }

    const oldUrisString = formApi.getValue(props.formField);
    let oldUris = [];

    if (type(oldUrisString) == "String") {
      try {
        oldUris = JSON.parse(oldUrisString);
      } catch (error) {
        console.log(error);
      }
    } else if (type(oldUrisString) == "Array") {
      oldUris = oldUrisString;
    }

    if (props?.multiple === false) {
      oldUris = [];
    }

    const uris = oldUris.concat(responseBody?.data?.uris);

    const res = {
      ...file,
      uri: responseBody?.data?.uris?.[0],
      uris: uris,
      ...mediaInfo,
    };

    if (responseBody?.data?.goodNum >= 1) {
      if (typeof props.onSuccess === "function") {
        props.onSuccess(res);
      } else if (props?.multiple === false) {
        // 单文件上传，单独处理成字符串
        if (props.singleAsMultiple) {
          updateFormUris(uris);
        } else {
          formApi.setValue(props.formField, uris[0]);
        }
      } else {
        updateFormUris(uris);
        /* if (!props.arrayProcessType) {
          //默认还是string
          formApi.setValue(props.formField, JSON.stringify(uris));
        } else if (props.arrayProcessType === 'array') {
          // 前后端统一接口：JSON标准格式，后端内部转换为字符串
          formApi.setValue(props.formField, uris);
        } else if (props.arrayProcessType === 'string') {
          //设置了string
          formApi.setValue(props.formField, JSON.stringify(uris));
        } else {
          //未设置，默认还是string
          formApi.setValue(props.formField, JSON.stringify(uris));
        } */
      }
    }
  };

  const befreonRemove = (file, fileList) => {
    console.log("befreonRemove");
    console.log(file);
    console.log(fileList);
  };

  const onRemove = (currentFile, fileList, currentFileItem) => {
    /* console.log('onRemove');
    console.log(currentFile);
    console.log(fileList);
    console.log(currentFileItem); */

    /* const newFileList = fileList.filter(o => o.uid !== currentFileItem.uid)
    console.log(newFileList); */

    const uris = fileList
      .filter((o) => o.uid !== currentFileItem.uid)
      .map((file) => {
        try {
          const url = new URL(file.url);
          return url.pathname;
        } catch (error) {
          // 解析失败，说明是无host部分的pathnme，直接获取即可
          console.log(error);
          return file.url;
        }
      });
    console.log(uris);

    updateFormUris(uris);
  };

  // Dreprecated: 代码库
  const onChange = async ({ fileList, currentFile }) => {
    console.log("onChange");
    console.log(fileList);
    console.log(currentFile);
    const newFileList: any[] = [];

    let mediaInfo = {};
    if (
      props.enableMediaInfo &&
      currentFile &&
      currentFile.status === "success"
    ) {
      const file = currentFile.fileInstance;
      mediaInfo = await getMediaInfo(file);
      console.log(mediaInfo);
    }

    fileList.forEach(async (o: any) => {
      if (o.status === "success") {
        let obj = {
          ...o,
        };
        if (o?.response?.data) {
          obj = {
            ...obj,
            url: o?.response?.data?.uris?.[0],
          };
        }
        if (props.enableMediaInfo && o?.uid === currentFile.uid) {
          obj = {
            ...obj,
            ...mediaInfo,
          };
        }
        newFileList.push(obj);
      }
    });

    const uris = newFileList.map((file) => {
      try {
        const url = new URL(file.url);
        return url.pathname;
      } catch (error) {
        // 解析失败，说明是无host部分的pathnme，直接获取即可
        console.log(error);
        return file.url;
      }
    });
    console.log(uris);

    if (!props.arrayProcessType) {
      //默认还是string
      formApi.setValue(props.formField, JSON.stringify(uris));
    } else if (props.arrayProcessType === "array") {
      // 前后端统一接口：JSON标准格式，后端内部转换为字符串
      formApi.setValue(props.formField, uris);
    } else if (props.arrayProcessType === "string") {
      //设置了string
      formApi.setValue(props.formField, JSON.stringify(uris));
    } else {
      //未设置，默认还是string
      formApi.setValue(props.formField, JSON.stringify(uris));
    }

    // updateList(newFileList);
  };

  // 这种方式也可以，但是无法 disable 掉最后一个虚线框
  // useEffect(() => {
  //   if (formState.values[props.formField]?.length >= 1) {
  //     formApi.setValue(props.field, JSON.parse(formState.values[props.formField]).map(url => {
  //       if (typeof url !== 'string') {
  //         url = `${url}`;
  //       }
  //       return {
  //         name: url?.includes?.('data:image') ? ulid() : url?.split('/').pop(),
  //         status: 'success',
  //         preview: true,
  //         url: url?.includes('data:image') ? url : `${import.meta.env.VITE_BASE}${url}`
  //       };
  //     }));
  //   }
  // }, []);

  //TODO 这个实现有问题
  /*
  const fileList = useMemo(() => {
    const formField = formState.values[props.field]

    if (Array.isArray(formField)) {
      return formField.map((url = '') => {

        if (url?.includes?.('data:image')) {
          return {
            name: ulid(),
            url,
          };
        }
        return {
          name: url?.split?.('/')?.pop(),
          url: `${base_url}${url}`,
        };
      });
    }
    // return [] => 处理单个上传文件，返回为字符串的情况
    return [{
      name: formField?.split?.('/')?.pop(),
      url: `${base_url}${formField}`,
    }];
  }, []);


  if (props.disabled && props?.type !== 'file') {

    return <Form.Slot
      label={{
        text: props.label,
        required: props.isRequired,
        align: 'right',
      }}>
      <div className="flex flex-wrap justify-start items-start gap-2">
        <ImagePreview>
          {(fileList ?? []).map(({ name, url }) => <div className="group relative">
            <div className="rounded-[3px] w-[96px] h-[96px] invisible bg-[color:var(--semi-color-overlay-bg)] absolute left-0 top-0 right-0 bottom-0 group-hover:visible" />
            <Image
              key={name}
              src={url}
              width={96}
              height={96}
              className="rounded-[3px] border border-solid border-[#dadada] relative z-[1]" />
          </div>)}
        </ImagePreview>
      </div>
    </Form.Slot>
  }*/

  const renderFileOperation = (fileItem) => {
    return (
      <div style={{ display: "flex", columnGap: 8, padding: "0 8px" }}>
        <Button
          icon={<IconEyeOpened></IconEyeOpened>}
          type="tertiary"
          theme="borderless"
          size="small"
        ></Button>
        <a href={fileItem.url}>
          <Button
            icon={<IconDownload></IconDownload>}
            type="tertiary"
            theme="borderless"
            size="small"
          ></Button>
        </a>
        <Button
          onClick={(e) => fileItem.onRemove()}
          icon={<IconDelete></IconDelete>}
          type="tertiary"
          theme="borderless"
          size="small"
        ></Button>
      </div>
    );
  };

  // 生成提示文本的函数
  const generateHelpText = () => {
    const parts: string[] = [];

    // 处理文件格式
    if (props.accept) {
      const extensions = props.accept
        .split(",")
        .map((ext) => ext.trim().replace(".", ""))
        .join("、");
      parts.push(`支持${extensions}格式`);
    }

    // 处理文件大小
    if (props.maxSize) {
      const sizeInGB = props.maxSize / (1024 * 1024);
      const sizeInMB = props.maxSize / 1024;
      const sizeInKB = props.maxSize;
      if (sizeInGB >= 1) {
        parts.push(`大小不超过${sizeInGB.toFixed(2)}GB`);
      } else if (sizeInMB >= 1) {
        parts.push(`大小不超过${sizeInMB.toFixed(2)}MB`);
      } else {
        parts.push(`大小不超过${sizeInKB.toFixed(2)}KB`);
      }
    }

    return parts.join("，");
  };

  const isImg = Boolean((props?.type ?? "img") === "img");
  const listType = props?.listType ?? "picture";

  const defaultHelpTextStyle: React.CSSProperties = {
    fontSize: "12px",
    color: "rgba(0, 0, 0, 0.45)",
    marginTop: "8px",
    lineHeight: "1.5",
  };

  const shouldShowHelpText = props.showHelpText !== false;
  const helpText = generateHelpText();

  // 自定义删除按钮 - 解决白色背景图片上删除按钮不清晰的问题
  const renderPicClose = ({ className, remove }) => {
    return (
      <div
        className={className}
        onClick={remove}
        style={{
          background: "rgba(0, 0, 0, 0.85)",
          border: "2px solid rgba(255, 255, 255, 0.95)",
          color: "white",
          backdropFilter: "blur(8px)",
          transition: "all 0.2s ease",
          boxShadow:
            "0 3px 10px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.2) inset",
          width: "26px",
          height: "26px",
          borderRadius: "50%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          zIndex: 10,
          position: "absolute",
          top: "6px",
          right: "6px",
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = "rgba(220, 38, 38, 0.95)";
          e.currentTarget.style.borderColor = "white";
          e.currentTarget.style.transform = "scale(1.2)";
          e.currentTarget.style.boxShadow =
            "0 5px 15px rgba(220, 38, 38, 0.5), 0 0 0 3px rgba(255, 255, 255, 0.9)";
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = "rgba(0, 0, 0, 0.85)";
          e.currentTarget.style.borderColor = "rgba(255, 255, 255, 0.95)";
          e.currentTarget.style.transform = "scale(1)";
          e.currentTarget.style.boxShadow =
            "0 3px 10px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.2) inset";
        }}
        onMouseDown={(e) => {
          e.currentTarget.style.transform = "scale(1.1)";
        }}
        onMouseUp={(e) => {
          e.currentTarget.style.transform = "scale(1.2)";
        }}
      >
        <IconDelete
          style={{
            color: "white",
            fontSize: "16px",
            fontWeight: "bold",
            textShadow: "0 2px 4px rgba(0, 0, 0, 0.6)",
          }}
        />
      </div>
    );
  };

  return (
    <>
      <Form.Upload
        {...props}
        // field={props.formField}
        // formField={props.formField}
        // label={props.label}

        renderFileOperation={renderFileOperation}
        headers={{
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        }}
        listType={listType}
        // accept={isImg ? "image/*" : 'application/*'}
        accept={isImg ? "image/*" : props.accept}
        // limit={props?.multiple ? 10 : 1}
        // 与上一句不同，当multiple没有传参时，默认limit=10，即支持多文件上传
        maxSize={props.maxSize}
        onSizeError={(file, fileList) =>
          Toast.error(`${file.name} 大小超过限制`)
        }
        limit={props.multiple === true ? 10 : props.multiple === false ? 1 : 10}
        action={upload_url}
        onSuccess={onSuccess}
        onRemove={onRemove}
        // onChange={onChange}
        // beforeRemove={befreonRemove}
        data={{
          //...(props?.uploadData),
          type: isImg ? 1 : 2,
        }}
        uploadTrigger="auto"
        renderThumbnail={
          listType === "picture" ? (
            (file) => {
              return <Image src={file.url} />;
            }
          ) : (
            <></>
          )
        }
        renderPicClose={listType === "picture" ? renderPicClose : undefined}
      >
        {props.children || (
          <span
            className={
              props.disabled
                ? "cursor-not-allowed"
                : "text-[color:var(--semi-color-link)]"
            }
          >
            <IconUpload className="align-middle mr-1" />
            {isImg ? "上传图片" : "上传文件"}
          </span>
        )}
      </Form.Upload>
      {shouldShowHelpText && helpText && (
        <div style={{ ...defaultHelpTextStyle, ...props.helpTextStyle }}>
          {helpText}
        </div>
      )}
    </>
  );
};
