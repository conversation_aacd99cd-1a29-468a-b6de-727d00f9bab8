@layer tailwind-base,semi,components,utils;
@layer tailwind-base {
  @tailwind base;
}
@layer tailwind-components {
  @tailwind components;
}
@layer tailwind-utils {
  @tailwind utilities;
}

@layer base {
  @font-face {
    font-family: Inter;
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url("/fonts/Inter-Regular.woff2") format("woff2");
  }
  @font-face {
    font-family: Inter;
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url("/fonts/Inter-Medium.woff2") format("woff2");
  }
  @font-face {
    font-family: Inter;
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url("/fonts/Inter-Bold.woff2") format("woff2");
  }

  [type="text"],
  input:where(:not([type])),
  [type="email"],
  [type="url"],
  [type="password"],
  [type="number"],
  [type="date"],
  [type="datetime-local"],
  [type="month"],
  [type="search"],
  [type="tel"],
  [type="time"],
  [type="week"],
  [multiple],
  textarea,
  select {
    font-size: 14px;
    background-color: transparent;
  }

  [type="text"]:focus {
    outline: none;
    box-shadow: none;
  }
}

html,
body {
  height: 100%;
}

@layer components {
}

.semi-table-thead > .semi-table-row > .semi-table-row-head {
  @apply bg-gray-50;
}

.semi-table-thead
  > .semi-table-row
  > .semi-table-row-head.semi-table-cell-fixed-left,
.semi-table-thead
  > .semi-table-row
  > .semi-table-row-head.semi-table-cell-fixed-right {
  @apply bg-gray-50;
}

.semi-table-thead
  > .semi-table-row
  > .semi-table-row-head.semi-table-cell-fixed-left::before,
.semi-table-thead
  > .semi-table-row
  > .semi-table-row-head.semi-table-cell-fixed-right::before {
  @apply bg-gray-50;
}

.semi-modal-centered {
  max-height: 100%;
  overflow: auto;
}

.semi-navigation-vertical .semi-navigation-header-list-outer {
  height: calc(100% - 64px);
}

.semi-navigation-vertical
  .semi-navigation-header-list-outer
  .semi-navigation-list-wrapper {
  height: 100%;
}

.unit-safety-signs .semi-checkbox-cardType {
  padding: 1px;
  width: fit-content;
  height: fit-content;
}

.unit-safety-signs .semi-checkbox-cardType .semi-checkbox-inner {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
}

.safety-signs-col .flex > .semi-form-field {
  padding-top: 0;
  padding-bottom: 0;
}

.semi-tree-option-list-block .semi-tree-option-label-text {
  width: fit-content;
  white-space: nowrap;
  padding-right: 10px;
}

.semi-descriptions-custom-small,
.semi-descriptions-custom-small .semi-descriptions-key,
.semi-descriptions-custom-small .semi-descriptions-value {
  font-size: 12px;
  line-height: 16px;
}

.semi-descriptions.semi-descriptions-custom-small .semi-descriptions-item {
  padding-bottom: 2px;
}

* > div[data-tip="下载"] {
  display: none;
}

* > div[data-tip="上传"] {
  display: none;
}

* > div[data-tip="打印"] {
  display: none;
}

html {
  --bigScreenModal: rgb(1 1 1 / 100%);
  --bigScreenModal20: rgb(1 1 1 / 20%);
  --modalBoxBg: rgb(1 1 1 / 40%);
  --bigScreenModal30: rgb(1 1 1 / 30%);
  --modalBackground: rgb(1 1 1 / 60%);
  --modalBackground: rgb(1 1 1 / 70%);
  --modalBackground: rgb(1 1 1 / 80%);
}

/* Upload组件图片模式删除按钮样式优化 - 解决白色背景图片上删除按钮不清晰的问题 */
.semi-upload-file-card .semi-upload-file-card-remove {
  background: rgba(0, 0, 0, 0.75) !important;
  border: 2px solid rgba(255, 255, 255, 0.9) !important;
  color: white !important;
  backdrop-filter: blur(6px);
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3),
              0 0 0 1px rgba(0, 0, 0, 0.1) inset !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
}

.semi-upload-file-card .semi-upload-file-card-remove:hover {
  background: rgba(220, 38, 38, 0.9) !important;
  border-color: white !important;
  transform: scale(1.15);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4),
              0 0 0 2px rgba(255, 255, 255, 0.8) !important;
}

.semi-upload-file-card .semi-upload-file-card-remove .semi-icon {
  color: white !important;
  font-size: 14px !important;
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.semi-upload-file-card .semi-upload-file-card-remove:active {
  transform: scale(1.05);
}
